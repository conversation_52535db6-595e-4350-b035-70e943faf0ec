{"name": "@npmcli/fs", "version": "1.1.1", "description": "filesystem utilities for the npm cli", "main": "lib/index.js", "files": ["bin", "lib"], "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint '**/*.js'", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "npm-template-check"}, "keywords": ["npm", "oss"], "author": "GitHub Inc.", "license": "ISC", "devDependencies": {"@npmcli/template-oss": "^2.3.1", "tap": "^15.0.9"}, "dependencies": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}, "templateVersion": "2.3.1"}