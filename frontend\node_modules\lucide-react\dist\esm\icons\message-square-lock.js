/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M19 15v-2a2 2 0 1 0-4 0v2", key: "h3d1vz" }],
  ["path", { d: "M9 17H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v3.5", key: "xsnnhn" }],
  ["rect", { x: "13", y: "15", width: "8", height: "5", rx: "1", key: "1ccwuk" }]
];
const MessageSquareLock = createLucideIcon("message-square-lock", __iconNode);

export { __iconNode, MessageSquareLock as default };
//# sourceMappingURL=message-square-lock.js.map
