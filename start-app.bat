@echo off
echo Starting Email Marketing Platform...
echo.

echo Starting Backend Server...
start "Backend Server" cmd /k "cd mock-backend && node server.js"

timeout /t 3 /nobreak > nul

echo Starting Frontend Server...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo Both servers are starting...
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3001
echo.
echo Demo Login:
echo Email: <EMAIL>
echo Password: password
echo.
echo Press any key to exit...
pause > nul
