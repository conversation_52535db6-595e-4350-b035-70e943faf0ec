import React, { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Save, Eye, Code, Smartphone, Monitor, ArrowLeft } from 'lucide-react'

const TemplateEditor: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)

  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [viewMode, setViewMode] = useState<'visual' | 'code'>('visual')
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop')
  const [htmlContent, setHtmlContent] = useState(`
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
      <h1 style="color: #333; text-align: center;">Welcome to our Newsletter</h1>
      <p style="color: #666; line-height: 1.6;">
        This is a sample email template. You can customize this content using our visual editor.
      </p>
      <div style="text-align: center; margin: 20px 0;">
        <a href="#" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">
          Call to Action
        </a>
      </div>
    </div>
  `)

  const handleSave = async () => {
    // TODO: Implement save functionality
    console.log('Saving template:', {
      name: templateName,
      description: templateDescription,
      html_content: htmlContent,
    })
    navigate('/templates')
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/templates')}
              className="btn btn-outline p-2"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit Template' : 'Create Template'}
              </h1>
              <p className="text-sm text-gray-600">
                Design your email template with our visual editor
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('visual')}
                className={`px-3 py-1 text-sm font-medium rounded-md ${
                  viewMode === 'visual'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <Eye className="w-4 h-4 mr-1 inline" />
                Visual
              </button>
              <button
                onClick={() => setViewMode('code')}
                className={`px-3 py-1 text-sm font-medium rounded-md ${
                  viewMode === 'code'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <Code className="w-4 h-4 mr-1 inline" />
                Code
              </button>
            </div>
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPreviewMode('desktop')}
                className={`px-3 py-1 text-sm font-medium rounded-md ${
                  previewMode === 'desktop'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <Monitor className="w-4 h-4" />
              </button>
              <button
                onClick={() => setPreviewMode('mobile')}
                className={`px-3 py-1 text-sm font-medium rounded-md ${
                  previewMode === 'mobile'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <Smartphone className="w-4 h-4" />
              </button>
            </div>
            <button onClick={handleSave} className="btn btn-primary">
              <Save className="w-4 h-4 mr-2" />
              Save Template
            </button>
          </div>
        </div>
      </div>

      {/* Template Settings */}
      <div className="bg-white border-b px-6 py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Template Name
            </label>
            <input
              type="text"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              className="input"
              placeholder="Enter template name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <input
              type="text"
              value={templateDescription}
              onChange={(e) => setTemplateDescription(e.target.value)}
              className="input"
              placeholder="Enter template description"
            />
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 flex">
        {viewMode === 'visual' ? (
          <>
            {/* Component Palette */}
            <div className="w-64 bg-white border-r p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Components</h3>
              <div className="space-y-2">
                <div className="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <div className="text-sm font-medium">Text Block</div>
                  <div className="text-xs text-gray-500">Add text content</div>
                </div>
                <div className="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <div className="text-sm font-medium">Image</div>
                  <div className="text-xs text-gray-500">Add an image</div>
                </div>
                <div className="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <div className="text-sm font-medium">Button</div>
                  <div className="text-xs text-gray-500">Add a call-to-action</div>
                </div>
                <div className="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <div className="text-sm font-medium">Divider</div>
                  <div className="text-xs text-gray-500">Add a separator</div>
                </div>
              </div>
            </div>

            {/* Canvas */}
            <div className="flex-1 bg-gray-100 p-6">
              <div
                className={`mx-auto bg-white shadow-lg ${
                  previewMode === 'mobile' ? 'max-w-sm' : 'max-w-2xl'
                }`}
              >
                <div
                  dangerouslySetInnerHTML={{ __html: htmlContent }}
                  className="p-6"
                />
              </div>
            </div>

            {/* Properties Panel */}
            <div className="w-64 bg-white border-l p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Properties</h3>
              <div className="text-sm text-gray-500">
                Select an element to edit its properties
              </div>
            </div>
          </>
        ) : (
          /* Code Editor */
          <div className="flex-1 p-6">
            <div className="h-full">
              <textarea
                value={htmlContent}
                onChange={(e) => setHtmlContent(e.target.value)}
                className="w-full h-full p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter your HTML content here..."
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default TemplateEditor
