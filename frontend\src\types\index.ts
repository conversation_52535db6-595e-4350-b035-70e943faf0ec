export interface User {
  id: number
  email: string
  name: string
  created_at: string
}

export interface AuthResponse {
  user: User
  token: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterCredentials {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface Template {
  id: number
  name: string
  description?: string
  html_content: string
  thumbnail?: string
  created_at: string
  updated_at: string
  user_id: number
}

export interface Contact {
  id: number
  email: string
  name?: string
  status: 'active' | 'unsubscribed' | 'bounced'
  created_at: string
  updated_at: string
  user_id: number
}

export interface ContactList {
  id: number
  name: string
  description?: string
  contacts: Contact[]
  created_at: string
  updated_at: string
  user_id: number
}

export interface Campaign {
  id: number
  name: string
  subject: string
  template_id: number
  template?: Template
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused'
  scheduled_at?: string
  sent_at?: string
  total_recipients: number
  sent_count: number
  delivered_count: number
  opened_count: number
  clicked_count: number
  bounced_count: number
  created_at: string
  updated_at: string
  user_id: number
}

export interface EmailLog {
  id: number
  campaign_id: number
  contact_id: number
  status: 'pending' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'failed'
  sent_at?: string
  delivered_at?: string
  opened_at?: string
  clicked_at?: string
  error_message?: string
  created_at: string
}

export interface TemplateComponent {
  id: string
  type: 'text' | 'image' | 'button' | 'divider' | 'spacer'
  content: any
  styles: Record<string, any>
  position: { x: number; y: number }
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}
