const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const PORT = 8000;
const JWT_SECRET = 'your-secret-key';

// Middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true
}));
app.use(express.json());

// Database setup
const dbPath = path.join(__dirname, 'database.sqlite');
const db = new sqlite3.Database(dbPath);

// Initialize database
db.serialize(() => {
  // Users table
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Templates table
  db.run(`
    CREATE TABLE IF NOT EXISTS templates (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      html_content TEXT NOT NULL,
      thumbnail TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Contacts table
  db.run(`
    CREATE TABLE IF NOT EXISTS contacts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      email TEXT NOT NULL,
      name TEXT,
      status TEXT DEFAULT 'active',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Campaigns table
  db.run(`
    CREATE TABLE IF NOT EXISTS campaigns (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      subject TEXT NOT NULL,
      template_id INTEGER NOT NULL,
      status TEXT DEFAULT 'draft',
      scheduled_at DATETIME NULL,
      sent_at DATETIME NULL,
      total_recipients INTEGER DEFAULT 0,
      sent_count INTEGER DEFAULT 0,
      delivered_count INTEGER DEFAULT 0,
      opened_count INTEGER DEFAULT 0,
      clicked_count INTEGER DEFAULT 0,
      bounced_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE CASCADE
    )
  `);

  // Create demo user
  const hashedPassword = bcrypt.hashSync('password', 10);
  db.run(`
    INSERT OR IGNORE INTO users (name, email, password)
    VALUES (?, ?, ?)
  `, ['Demo User', '<EMAIL>', hashedPassword], function() {
    const userId = 1; // Demo user ID

    // Add sample templates
    db.run(`
      INSERT OR IGNORE INTO templates (id, user_id, name, description, html_content)
      VALUES (?, ?, ?, ?, ?)
    `, [1, userId, 'Welcome Newsletter', 'A warm welcome email for new subscribers', `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;">
        <h1 style="color: #2563eb; text-align: center; margin-bottom: 30px;">Welcome to Our Newsletter!</h1>
        <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
          Thank you for subscribing to our newsletter. We're excited to have you on board!
        </p>
        <p style="color: #374151; line-height: 1.6; margin-bottom: 30px;">
          You'll receive the latest updates, exclusive content, and special offers directly in your inbox.
        </p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="#" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Get Started
          </a>
        </div>
        <p style="color: #6b7280; font-size: 14px; text-align: center;">
          Best regards,<br>The Team
        </p>
      </div>
    `]);

    db.run(`
      INSERT OR IGNORE INTO templates (id, user_id, name, description, html_content)
      VALUES (?, ?, ?, ?, ?)
    `, [2, userId, 'Product Announcement', 'Perfect for announcing new products or features', `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;">
        <h1 style="color: #dc2626; text-align: center; margin-bottom: 30px;">🚀 Exciting News!</h1>
        <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
          We're thrilled to announce our latest product that will revolutionize your workflow.
        </p>
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #1f2937; margin-top: 0;">Key Features:</h2>
          <ul style="color: #374151; line-height: 1.6;">
            <li>Advanced automation capabilities</li>
            <li>Seamless integration</li>
            <li>Enhanced user experience</li>
          </ul>
        </div>
        <div style="text-align: center; margin: 30px 0;">
          <a href="#" style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Learn More
          </a>
        </div>
      </div>
    `]);

    // Add sample contacts
    const sampleContacts = [
      ['<EMAIL>', 'John Doe'],
      ['<EMAIL>', 'Jane Smith'],
      ['<EMAIL>', 'Bob Johnson'],
      ['<EMAIL>', 'Alice Brown'],
      ['<EMAIL>', 'Charlie Wilson']
    ];

    sampleContacts.forEach(([email, name]) => {
      db.run(`
        INSERT OR IGNORE INTO contacts (user_id, email, name, status)
        VALUES (?, ?, ?, ?)
      `, [userId, email, name, 'active']);
    });

    // Add sample campaign
    db.run(`
      INSERT OR IGNORE INTO campaigns (id, user_id, name, subject, template_id, status, total_recipients, sent_count, delivered_count, opened_count, clicked_count)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [1, userId, 'Summer Sale Newsletter', '🌞 Summer Sale - Up to 50% Off!', 1, 'sent', 1200, 1200, 1180, 294, 38]);
  });
});

// Helper functions
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

const generateToken = (user) => {
  return jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });
};

// Auth routes
app.post('/api/auth/register', (req, res) => {
  const { name, email, password, confirmPassword } = req.body;

  if (!name || !email || !password || !confirmPassword) {
    return res.status(422).json({
      success: false,
      message: 'All fields are required',
      errors: {
        name: !name ? ['Name is required'] : [],
        email: !email ? ['Email is required'] : [],
        password: !password ? ['Password is required'] : [],
        confirmPassword: !confirmPassword ? ['Confirm password is required'] : []
      }
    });
  }

  if (password !== confirmPassword) {
    return res.status(422).json({
      success: false,
      message: 'Passwords do not match',
      errors: { confirmPassword: ['Passwords do not match'] }
    });
  }

  const hashedPassword = bcrypt.hashSync(password, 10);

  db.run(
    'INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
    [name, email, hashedPassword],
    function(err) {
      if (err) {
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(422).json({
            success: false,
            message: 'Email already registered',
            errors: { email: ['This email is already registered'] }
          });
        }
        return res.status(500).json({ success: false, message: 'Registration failed' });
      }

      db.get('SELECT * FROM users WHERE id = ?', [this.lastID], (err, user) => {
        if (err) {
          return res.status(500).json({ success: false, message: 'Registration failed' });
        }

        const token = generateToken(user);
        delete user.password;

        res.json({
          success: true,
          message: 'Registration successful',
          data: { user, token }
        });
      });
    }
  );
});

app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(422).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  db.get('SELECT * FROM users WHERE email = ?', [email], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({
        success: false,
        message: 'Login failed'
      });
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    console.log('User found:', user.email);
    console.log('Password check:', bcrypt.compareSync(password, user.password));

    if (!bcrypt.compareSync(password, user.password)) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    const token = generateToken(user);
    delete user.password;

    res.json({
      success: true,
      message: 'Login successful',
      data: { user, token }
    });
  });
});

app.get('/api/auth/verify', authenticateToken, (req, res) => {
  db.get('SELECT * FROM users WHERE id = ?', [req.user.id], (err, user) => {
    if (err || !user) {
      return res.status(401).json({ success: false, message: 'Invalid token' });
    }

    delete user.password;
    res.json({
      success: true,
      message: 'Token verified',
      data: user
    });
  });
});

app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Templates routes
app.get('/api/templates', authenticateToken, (req, res) => {
  db.all(
    'SELECT * FROM templates WHERE user_id = ? ORDER BY updated_at DESC',
    [req.user.id],
    (err, templates) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Failed to fetch templates' });
      }
      res.json({ success: true, data: templates });
    }
  );
});

app.post('/api/templates', authenticateToken, (req, res) => {
  const { name, description, html_content } = req.body;

  if (!name || !html_content) {
    return res.status(422).json({
      success: false,
      message: 'Name and HTML content are required'
    });
  }

  db.run(
    'INSERT INTO templates (user_id, name, description, html_content) VALUES (?, ?, ?, ?)',
    [req.user.id, name, description || null, html_content],
    function(err) {
      if (err) {
        return res.status(500).json({ success: false, message: 'Failed to create template' });
      }

      db.get('SELECT * FROM templates WHERE id = ?', [this.lastID], (err, template) => {
        if (err) {
          return res.status(500).json({ success: false, message: 'Failed to create template' });
        }

        res.json({
          success: true,
          message: 'Template created successfully',
          data: template
        });
      });
    }
  );
});

// Contacts routes
app.get('/api/contacts', authenticateToken, (req, res) => {
  db.all(
    'SELECT * FROM contacts WHERE user_id = ? ORDER BY created_at DESC',
    [req.user.id],
    (err, contacts) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Failed to fetch contacts' });
      }
      res.json({ success: true, data: contacts });
    }
  );
});

app.post('/api/contacts', authenticateToken, (req, res) => {
  const { email, name, status = 'active' } = req.body;

  if (!email) {
    return res.status(422).json({
      success: false,
      message: 'Email is required'
    });
  }

  db.run(
    'INSERT INTO contacts (user_id, email, name, status) VALUES (?, ?, ?, ?)',
    [req.user.id, email, name || null, status],
    function(err) {
      if (err) {
        return res.status(500).json({ success: false, message: 'Failed to create contact' });
      }

      db.get('SELECT * FROM contacts WHERE id = ?', [this.lastID], (err, contact) => {
        if (err) {
          return res.status(500).json({ success: false, message: 'Failed to create contact' });
        }

        res.json({
          success: true,
          message: 'Contact created successfully',
          data: contact
        });
      });
    }
  );
});

// Campaigns routes
app.get('/api/campaigns', authenticateToken, (req, res) => {
  db.all(
    `SELECT c.*, t.name as template_name 
     FROM campaigns c
     LEFT JOIN templates t ON c.template_id = t.id
     WHERE c.user_id = ? 
     ORDER BY c.created_at DESC`,
    [req.user.id],
    (err, campaigns) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Failed to fetch campaigns' });
      }
      res.json({ success: true, data: campaigns });
    }
  );
});

app.post('/api/campaigns', authenticateToken, (req, res) => {
  const { name, subject, template_id, status = 'draft', scheduled_at } = req.body;

  if (!name || !subject || !template_id) {
    return res.status(422).json({
      success: false,
      message: 'Name, subject, and template are required'
    });
  }

  db.run(
    'INSERT INTO campaigns (user_id, name, subject, template_id, status, scheduled_at) VALUES (?, ?, ?, ?, ?, ?)',
    [req.user.id, name, subject, template_id, status, scheduled_at || null],
    function(err) {
      if (err) {
        return res.status(500).json({ success: false, message: 'Failed to create campaign' });
      }

      db.get(
        `SELECT c.*, t.name as template_name 
         FROM campaigns c
         LEFT JOIN templates t ON c.template_id = t.id
         WHERE c.id = ?`,
        [this.lastID],
        (err, campaign) => {
          if (err) {
            return res.status(500).json({ success: false, message: 'Failed to create campaign' });
          }

          res.json({
            success: true,
            message: 'Campaign created successfully',
            data: campaign
          });
        }
      );
    }
  );
});

// Start server
app.listen(PORT, () => {
  console.log(`Mock backend server running on http://localhost:${PORT}`);
  console.log('Demo user: <EMAIL> / password');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  db.close((err) => {
    if (err) {
      console.error(err.message);
    }
    console.log('Database connection closed.');
    process.exit(0);
  });
});
