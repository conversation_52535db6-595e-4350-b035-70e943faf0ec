// Simple API test script
const http = require('http');

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 8000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const postData = JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  });

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('API Response:', JSON.parse(data));
    });
  });

  req.on('error', (e) => {
    console.error('API Error:', e.message);
  });

  req.write(postData);
  req.end();
}

console.log('Testing API connection...');
testAPI();
