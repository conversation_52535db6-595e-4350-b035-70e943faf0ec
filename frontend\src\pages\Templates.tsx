import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Plus, FileText, Edit, Trash2, Copy } from 'lucide-react'

const Templates: React.FC = () => {
  // Mock data - will be replaced with real API calls
  const templates = [
    {
      id: 1,
      name: 'Newsletter Template',
      description: 'A clean and modern newsletter template',
      thumbnail: null,
      created_at: '2024-01-10',
      updated_at: '2024-01-12',
    },
    {
      id: 2,
      name: 'Product Announcement',
      description: 'Template for announcing new products',
      thumbnail: null,
      created_at: '2024-01-08',
      updated_at: '2024-01-08',
    },
    {
      id: 3,
      name: 'Welcome Email',
      description: 'Welcome new subscribers with this template',
      thumbnail: null,
      created_at: '2024-01-05',
      updated_at: '2024-01-06',
    },
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Email Templates</h1>
          <p className="text-gray-600">Create and manage your email templates</p>
        </div>
        <Link
          to="/templates/new"
          className="btn btn-primary flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Template
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <div key={template.id} className="card overflow-hidden">
            <div className="h-48 bg-gray-100 flex items-center justify-center">
              {template.thumbnail ? (
                <img
                  src={template.thumbnail}
                  alt={template.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <FileText className="w-16 h-16 text-gray-400" />
              )}
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {template.name}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {template.description}
              </p>
              <div className="text-xs text-gray-500 mb-4">
                Updated {new Date(template.updated_at).toLocaleDateString()}
              </div>
              <div className="flex items-center space-x-2">
                <Link
                  to={`/templates/edit/${template.id}`}
                  className="btn btn-outline flex-1 flex items-center justify-center"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Link>
                <button className="btn btn-outline p-2">
                  <Copy className="w-4 h-4" />
                </button>
                <button className="btn btn-outline p-2 text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {templates.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates yet
          </h3>
          <p className="text-gray-600 mb-6">
            Get started by creating your first email template
          </p>
          <Link
            to="/templates/new"
            className="btn btn-primary"
          >
            Create Your First Template
          </Link>
        </div>
      )}
    </div>
  )
}

export default Templates
